"""
Clock configuration database model
"""

from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Boolean, ForeignKey, DateTime, Text, JSON
from sqlalchemy.orm import relationship

from app.core.database import Base


class ClockConfig(Base):
    __tablename__ = "clock_configs"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, index=True)
    config_name = Column(String(255), nullable=False, index=True)
    enabled = Column(Boolean, default=False, nullable=False)
    source = Column(String(255), nullable=False)
    frequency = Column(Integer, nullable=False)  # Frequency in Hz
    unit = Column(String(10), default="Hz", nullable=False)  # Hz, KHz, MHz
    description = Column(Text, nullable=True)
    device_tree_path = Column(String(512), nullable=True)  # Path in device tree
    kconfig_option = Column(String(255), nullable=True)  # Associated Kconfig option
    metadata = Column(JSON, nullable=True)  # Additional configuration metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = relationship("Project", back_populates="clock_configs")

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "config_name": self.config_name,
            "enabled": self.enabled,
            "source": self.source,
            "frequency": self.frequency,
            "unit": self.unit,
            "description": self.description,
            "device_tree_path": self.device_tree_path,
            "kconfig_option": self.kconfig_option,
            "metadata": self.metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    @property
    def frequency_hz(self) -> int:
        """Get frequency in Hz regardless of unit"""
        if self.unit == "KHz":
            return self.frequency * 1000
        elif self.unit == "MHz":
            return self.frequency * 1000000
        else:
            return self.frequency

    @classmethod
    def from_frequency_hz(cls, frequency_hz: int) -> tuple[int, str]:
        """Convert Hz to appropriate unit and value"""
        if frequency_hz >= 1000000 and frequency_hz % 1000000 == 0:
            return frequency_hz // 1000000, "MHz"
        elif frequency_hz >= 1000 and frequency_hz % 1000 == 0:
            return frequency_hz // 1000, "KHz"
        else:
            return frequency_hz, "Hz"
