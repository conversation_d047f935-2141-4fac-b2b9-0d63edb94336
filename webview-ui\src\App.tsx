import React, { useState, useEffect, useCallback } from 'react';
import { ClockConfigState, ZephyrProject, WebviewMessage, VSCodeAPI } from './types';
import { ClockConfigurator } from './components/ClockConfigurator';
import { ProjectInfo } from './components/ProjectInfo';
import { StatusIndicator } from './components/StatusIndicator';
import { ErrorMessage } from './components/ErrorMessage';
import { LoadingSpinner } from './components/LoadingSpinner';

// Get VSCode API
const vscode: VSCodeAPI = window.acquireVsCodeApi();

function App() {
  const [state, setState] = useState<ClockConfigState>({
    selectedProject: null,
    clockEnabled: false,
    clockSource: '',
    frequency: 0,
    unit: 'MHz',
    isLoading: false,
    error: null,
    availableSources: [],
    serverStatus: {
      isRunning: false,
      serverUrl: ''
    }
  });

  // Handle messages from VSCode extension
  const handleMessage = useCallback((event: MessageEvent) => {
    const message: WebviewMessage = event.data;
    
    switch (message.type) {
      case 'PROJECT_SELECTED':
        setState(prev => ({
          ...prev,
          selectedProject: message.payload.project,
          serverStatus: {
            ...prev.serverStatus,
            serverUrl: message.payload.serverUrl
          }
        }));
        break;

      case 'CLOCK_CONFIG_DATA':
        if (message.payload.success) {
          const configs = message.payload.data || [];
          if (configs.length > 0) {
            const config = configs[0]; // Use first config for now
            setState(prev => ({
              ...prev,
              clockEnabled: config.enabled,
              clockSource: config.source,
              frequency: config.frequency,
              unit: config.unit,
              isLoading: false,
              error: null
            }));
          }
        } else {
          setState(prev => ({
            ...prev,
            error: message.payload.error,
            isLoading: false
          }));
        }
        break;

      case 'CLOCK_CONFIG_UPDATED':
        if (message.payload.success) {
          setState(prev => ({
            ...prev,
            error: null,
            isLoading: false
          }));
        } else {
          setState(prev => ({
            ...prev,
            error: message.payload.error,
            isLoading: false
          }));
        }
        break;

      case 'SERVER_STATUS':
        setState(prev => ({
          ...prev,
          serverStatus: message.payload
        }));
        break;

      case 'ERROR':
        setState(prev => ({
          ...prev,
          error: message.payload.error,
          isLoading: false
        }));
        break;

      default:
        console.warn('Unknown message type:', message.type);
    }
  }, []);

  // Set up message listener
  useEffect(() => {
    window.addEventListener('message', handleMessage);
    
    // Notify extension that webview is ready
    vscode.postMessage({ type: 'WEBVIEW_READY' });

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage]);

  // Load clock configuration when project is selected
  useEffect(() => {
    if (state.selectedProject && state.serverStatus.isRunning) {
      setState(prev => ({ ...prev, isLoading: true }));
      vscode.postMessage({ 
        type: 'GET_CLOCK_CONFIG',
        payload: { projectId: state.selectedProject.path }
      });
    }
  }, [state.selectedProject, state.serverStatus.isRunning]);

  const handleClockConfigChange = (config: Partial<ClockConfigState>) => {
    setState(prev => ({ ...prev, ...config }));
  };

  const handleSaveConfig = () => {
    if (!state.selectedProject) {
      setState(prev => ({ ...prev, error: 'No project selected' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true }));
    
    const configData = {
      config: {
        projectId: state.selectedProject.path,
        configName: 'main_clock',
        enabled: state.clockEnabled,
        source: state.clockSource,
        frequency: state.frequency,
        unit: state.unit,
        description: 'Main clock configuration'
      }
    };

    vscode.postMessage({
      type: 'UPDATE_CLOCK_CONFIG',
      payload: configData
    });
  };

  const handleScanProject = () => {
    if (!state.selectedProject) {
      setState(prev => ({ ...prev, error: 'No project selected' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true }));
    
    vscode.postMessage({
      type: 'SCAN_PROJECT',
      payload: { projectPath: state.selectedProject.path }
    });
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  return (
    <div className="container">
      <div className="header">
        <h1>Zephyr Clock Configurator</h1>
        {state.isLoading && <LoadingSpinner />}
      </div>

      <StatusIndicator 
        isConnected={state.serverStatus.isRunning}
        serverUrl={state.serverStatus.serverUrl}
      />

      {state.error && (
        <ErrorMessage 
          message={state.error} 
          onDismiss={clearError}
        />
      )}

      {state.selectedProject ? (
        <>
          <ProjectInfo 
            project={state.selectedProject}
            onScanProject={handleScanProject}
            isScanning={state.isLoading}
          />
          
          <ClockConfigurator
            config={state}
            onChange={handleClockConfigChange}
            onSave={handleSaveConfig}
            disabled={!state.serverStatus.isRunning || state.isLoading}
          />
        </>
      ) : (
        <div className="project-info">
          <h2>No Project Selected</h2>
          <p>Please select a Zephyr project from the sidebar to configure clock settings.</p>
        </div>
      )}
    </div>
  );
}

export default App;
