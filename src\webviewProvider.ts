import * as vscode from 'vscode';
import * as path from 'path';
import { ZephyrProject, WebviewMessage, ClockConfig } from './types';
import { PythonServerManager } from './pythonServer';

export class ClockConfigWebviewProvider {
    private static readonly viewType = 'zephyr.clockConfig';
    private panel: vscode.WebviewPanel | undefined;
    private selectedProject: ZephyrProject | undefined;

    constructor(
        private readonly extensionUri: vscode.Uri,
        private readonly pythonServer: PythonServerManager
    ) {}

    async createOrShow(project?: ZephyrProject): Promise<void> {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // If we already have a panel, show it
        if (this.panel) {
            this.panel.reveal(column);
            if (project) {
                await this.selectProject(project);
            }
            return;
        }

        // Otherwise, create a new panel
        this.panel = vscode.window.createWebviewPanel(
            ClockConfigWebviewProvider.viewType,
            'Zephyr Clock Configurator',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist'),
                    vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'public')
                ]
            }
        );

        // Set the webview's initial html content
        this.panel.webview.html = this.getHtmlForWebview(this.panel.webview);

        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(
            async (message: WebviewMessage) => {
                await this.handleWebviewMessage(message);
            },
            undefined,
            []
        );

        // Handle panel disposal
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        }, null, []);

        // Select project if provided
        if (project) {
            await this.selectProject(project);
        }
    }

    async selectProject(project: ZephyrProject): Promise<void> {
        this.selectedProject = project;
        
        if (this.panel) {
            // Send project selection to webview
            await this.panel.webview.postMessage({
                type: 'PROJECT_SELECTED',
                payload: {
                    project: project,
                    serverUrl: this.pythonServer.getServerUrl()
                }
            });

            // Update panel title
            this.panel.title = `Clock Config - ${project.name}`;
        }
    }

    private async handleWebviewMessage(message: WebviewMessage): Promise<void> {
        switch (message.type) {
            case 'GET_CLOCK_CONFIG':
                await this.handleGetClockConfig(message.payload);
                break;
            
            case 'UPDATE_CLOCK_CONFIG':
                await this.handleUpdateClockConfig(message.payload);
                break;
            
            case 'SCAN_PROJECT':
                await this.handleScanProject(message.payload);
                break;
            
            case 'GET_SERVER_STATUS':
                await this.handleGetServerStatus();
                break;
            
            case 'WEBVIEW_READY':
                await this.handleWebviewReady();
                break;
            
            default:
                console.warn(`Unknown message type: ${message.type}`);
        }
    }

    private async handleGetClockConfig(payload: any): Promise<void> {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }

        try {
            const response = await this.pythonServer.makeRequest<ClockConfig[]>(
                `/projects/${encodeURIComponent(this.selectedProject.path)}/clock-config`
            );

            await this.panel?.webview.postMessage({
                type: 'CLOCK_CONFIG_DATA',
                payload: response
            });
        } catch (error) {
            await this.sendError(`Failed to get clock config: ${error}`);
        }
    }

    private async handleUpdateClockConfig(payload: any): Promise<void> {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }

        try {
            const response = await this.pythonServer.makeRequest<void>(
                `/projects/${encodeURIComponent(this.selectedProject.path)}/clock-config`,
                'POST',
                payload.config
            );

            await this.panel?.webview.postMessage({
                type: 'CLOCK_CONFIG_UPDATED',
                payload: response
            });
        } catch (error) {
            await this.sendError(`Failed to update clock config: ${error}`);
        }
    }

    private async handleScanProject(payload: any): Promise<void> {
        if (!this.selectedProject) {
            await this.sendError('No project selected');
            return;
        }

        try {
            const response = await this.pythonServer.makeRequest<any>(
                `/projects/scan`,
                'POST',
                { projectPath: this.selectedProject.path }
            );

            await this.panel?.webview.postMessage({
                type: 'PROJECT_SCANNED',
                payload: response
            });
        } catch (error) {
            await this.sendError(`Failed to scan project: ${error}`);
        }
    }

    private async handleGetServerStatus(): Promise<void> {
        const isRunning = this.pythonServer.isServerRunning();
        const serverUrl = this.pythonServer.getServerUrl();

        await this.panel?.webview.postMessage({
            type: 'SERVER_STATUS',
            payload: {
                isRunning,
                serverUrl
            }
        });
    }

    private async handleWebviewReady(): Promise<void> {
        // Send initial data when webview is ready
        await this.handleGetServerStatus();
        
        if (this.selectedProject) {
            await this.selectProject(this.selectedProject);
        }
    }

    private async sendError(error: string): Promise<void> {
        await this.panel?.webview.postMessage({
            type: 'ERROR',
            payload: { error }
        });
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        // Get the local path to main script run in the webview
        const scriptPathOnDisk = vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets');
        const scriptUri = webview.asWebviewUri(scriptPathOnDisk);

        // Get the local path to CSS file
        const stylePathOnDisk = vscode.Uri.joinPath(this.extensionUri, 'webview-ui', 'dist', 'assets');
        const styleUri = webview.asWebviewUri(stylePathOnDisk);

        // Use a nonce to only allow specific scripts to be run
        const nonce = this.getNonce();

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; connect-src http://127.0.0.1:8000 ws://127.0.0.1:8000;">
                <title>Zephyr Clock Configurator</title>
                <base href="${scriptUri}/">
            </head>
            <body>
                <div id="root"></div>
                <script type="module" nonce="${nonce}" src="${scriptUri}/index.js"></script>
            </body>
            </html>`;
    }

    private getNonce(): string {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
