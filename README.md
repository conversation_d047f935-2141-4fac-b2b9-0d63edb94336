# Zephyr Clock Configurator VSCode Extension

A comprehensive VSCode extension for configuring clock settings in Zephyr RTOS projects. This extension provides a graphical interface for managing clock configurations with real-time project scanning and file monitoring.

## Features

- **Project Detection**: Automatically scans workspace for Zephyr projects
- **Clock Configuration**: Intuitive UI for setting clock sources and frequencies
- **Real-time Monitoring**: Watches project files for changes and updates configurations
- **Multi-format Support**: Parses device tree (.dtsi), YAML, and Kconfig files
- **Backend Integration**: Python FastAPI backend for robust file parsing and data management

## Architecture

The extension consists of three main components:

1. **VSCode Extension** (TypeScript): Provides the main extension interface and project tree view
2. **React Webview UI** (TypeScript): Interactive clock configuration interface
3. **Python Backend** (FastAPI): File parsing, database management, and API services

## Installation

### Prerequisites

- VSCode 1.74.0 or higher
- Node.js 18+ 
- Python 3.9+
- Zephyr SDK (for testing with real projects)

### Setup Instructions

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd zephyr-clock-configurator
   ```

2. **Install VSCode extension dependencies**:
   ```bash
   npm install
   ```

3. **Install React UI dependencies**:
   ```bash
   cd webview-ui
   npm install
   cd ..
   ```

4. **Install Python backend dependencies**:
   ```bash
   cd python-backend
   pip install -r requirements.txt
   cd ..
   ```

5. **Build the extension**:
   ```bash
   npm run compile
   npm run build-webview
   ```

6. **Install the extension**:
   - Open VSCode
   - Press `F5` to launch Extension Development Host
   - Or package with `vsce package` and install the .vsix file

## Usage

### Getting Started

1. **Open a Zephyr project** in VSCode
2. **Open the Zephyr Clock Config view** from the Activity Bar (clock icon)
3. **Select a project** from the project tree
4. **Launch the configurator** by clicking the gear icon or using the command palette

### Clock Configuration

1. **Enable clock configuration** using the checkbox
2. **Select a clock source** from the dropdown (HSI, HSE, PLL, etc.)
3. **Set the frequency** and choose the appropriate unit (Hz, KHz, MHz)
4. **Save the configuration** to update project files

### Project Scanning

- Projects are automatically scanned when opened
- Use the "Rescan Project" button to manually refresh
- The extension monitors file changes and updates automatically

## Development

### Project Structure

```
zephyr-clock-configurator/
├── src/                          # VSCode extension source
│   ├── extension.ts              # Main extension entry point
│   ├── treeDataProvider.ts       # Project tree view
│   ├── webviewProvider.ts        # React webview integration
│   ├── pythonServer.ts           # Python backend communication
│   └── utils/                    # Utility modules
├── webview-ui/                   # React TypeScript UI
│   ├── src/
│   │   ├── App.tsx               # Main React component
│   │   ├── components/           # UI components
│   │   └── types.ts              # TypeScript definitions
├── python-backend/               # Python FastAPI backend
│   ├── app/
│   │   ├── api/                  # API routes
│   │   ├── core/                 # Core configuration
│   │   ├── models/               # Database models
│   │   ├── schemas/              # Pydantic schemas
│   │   └── services/             # Business logic
│   └── main.py                   # FastAPI application
└── package.json                  # Extension manifest
```

### Building and Testing

1. **Compile TypeScript**:
   ```bash
   npm run compile
   ```

2. **Build React UI**:
   ```bash
   npm run build-webview
   ```

3. **Start Python backend** (for development):
   ```bash
   cd python-backend
   python main.py
   ```

4. **Run tests**:
   ```bash
   npm test
   ```

5. **Launch Extension Development Host**:
   ```bash
   code --extensionDevelopmentPath=.
   ```

### API Endpoints

The Python backend provides the following REST API endpoints:

- `GET /api/projects` - List all projects
- `POST /api/projects/scan` - Scan a project for configurations
- `GET /api/projects/{project_path}/clock-config` - Get clock configurations
- `POST /api/projects/{project_path}/clock-config` - Update clock configuration

## Configuration

### Extension Settings

The extension can be configured through VSCode settings:

- `zephyr.clockConfig.autoScan`: Enable automatic project scanning
- `zephyr.clockConfig.pythonPath`: Custom Python executable path
- `zephyr.clockConfig.serverPort`: Backend server port (default: 8000)

### File Patterns

The extension monitors the following file types:
- Device tree files: `*.dtsi`, `*.overlay`
- Configuration files: `prj.conf`, `*.conf`
- YAML files: `*.yaml`, `*.yml`
- Build files: `CMakeLists.txt`
- Kconfig files: `Kconfig*`

## Troubleshooting

### Common Issues

1. **Python backend fails to start**:
   - Ensure Python 3.9+ is installed and in PATH
   - Check that all dependencies are installed: `pip install -r requirements.txt`
   - Verify port 8000 is not in use

2. **Projects not detected**:
   - Ensure the workspace contains valid Zephyr projects
   - Check for `CMakeLists.txt` or `prj.conf` files
   - Use "Refresh Projects" command

3. **Clock configuration not saving**:
   - Verify backend server is running
   - Check file permissions in project directory
   - Review error messages in the extension output

### Debug Mode

Enable debug logging by setting the environment variable:
```bash
export ZEPHYR_DEBUG=1
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style

- TypeScript: Follow ESLint configuration
- Python: Follow PEP 8 style guide
- React: Use functional components with hooks

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the extension output logs
