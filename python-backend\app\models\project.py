"""
Project database model
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship

from app.core.database import Base


class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    path = Column(String(512), unique=True, nullable=False, index=True)
    board_config = Column(String(255), nullable=True)
    cmake_file = Column(String(512), nullable=True)
    prj_conf_file = Column(String(512), nullable=True)
    dtsi_files = Column(JSON, nullable=True)  # List of DTSI file paths
    yaml_files = Column(JSON, nullable=True)  # List of YAML file paths
    kconfig_files = Column(JSON, nullable=True)  # List of Kconfig file paths
    metadata = Column(JSON, nullable=True)  # Additional project metadata
    last_scanned = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    clock_configs = relationship("ClockConfig", back_populates="project", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "path": self.path,
            "board_config": self.board_config,
            "cmake_file": self.cmake_file,
            "prj_conf_file": self.prj_conf_file,
            "dtsi_files": self.dtsi_files or [],
            "yaml_files": self.yaml_files or [],
            "kconfig_files": self.kconfig_files or [],
            "metadata": self.metadata or {},
            "last_scanned": self.last_scanned.isoformat() if self.last_scanned else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
