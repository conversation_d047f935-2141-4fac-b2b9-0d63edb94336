"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PythonServerManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const cp = __importStar(require("child_process"));
const axios_1 = __importDefault(require("axios"));
class PythonServerManager {
    constructor(context) {
        this.context = context;
        this.serverProcess = null;
        this.isStarting = false;
        this.isRunning = false;
        this.config = {
            host: '127.0.0.1',
            port: 8000,
            autoStart: true
        };
    }
    async start() {
        if (this.isRunning || this.isStarting) {
            return true;
        }
        this.isStarting = true;
        try {
            // Check if server is already running
            if (await this.isServerRunning()) {
                this.isRunning = true;
                this.isStarting = false;
                return true;
            }
            // Start the Python server
            const pythonPath = await this.findPythonExecutable();
            const serverScript = path.join(this.context.extensionPath, 'python-backend', 'main.py');
            console.log(`Starting Python server: ${pythonPath} ${serverScript}`);
            this.serverProcess = cp.spawn(pythonPath, [serverScript], {
                cwd: path.join(this.context.extensionPath, 'python-backend'),
                env: {
                    ...process.env,
                    ZEPHYR_BACKEND_HOST: this.config.host,
                    ZEPHYR_BACKEND_PORT: this.config.port.toString(),
                    PYTHONPATH: path.join(this.context.extensionPath, 'python-backend')
                }
            });
            this.serverProcess.stdout?.on('data', (data) => {
                console.log(`Python server stdout: ${data}`);
            });
            this.serverProcess.stderr?.on('data', (data) => {
                console.error(`Python server stderr: ${data}`);
            });
            this.serverProcess.on('close', (code) => {
                console.log(`Python server exited with code ${code}`);
                this.isRunning = false;
                this.serverProcess = null;
            });
            this.serverProcess.on('error', (error) => {
                console.error(`Python server error: ${error}`);
                this.isRunning = false;
                this.serverProcess = null;
                vscode.window.showErrorMessage(`Failed to start Python backend: ${error.message}`);
            });
            // Wait for server to be ready
            const maxRetries = 30;
            let retries = 0;
            while (retries < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                if (await this.isServerRunning()) {
                    this.isRunning = true;
                    this.isStarting = false;
                    console.log('Python server is ready');
                    return true;
                }
                retries++;
            }
            throw new Error('Server failed to start within timeout period');
        }
        catch (error) {
            this.isStarting = false;
            console.error('Failed to start Python server:', error);
            vscode.window.showErrorMessage(`Failed to start Python backend: ${error}`);
            return false;
        }
    }
    async stop() {
        if (this.serverProcess) {
            console.log('Stopping Python server...');
            // Try graceful shutdown first
            try {
                await this.sendShutdownSignal();
                // Wait for graceful shutdown
                await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        console.log('Graceful shutdown timeout, forcing kill');
                        if (this.serverProcess) {
                            this.serverProcess.kill('SIGKILL');
                        }
                        resolve();
                    }, 5000);
                    if (this.serverProcess) {
                        this.serverProcess.on('exit', () => {
                            clearTimeout(timeout);
                            resolve();
                        });
                    }
                    else {
                        clearTimeout(timeout);
                        resolve();
                    }
                });
            }
            catch (error) {
                console.error('Error during graceful shutdown:', error);
                if (this.serverProcess) {
                    this.serverProcess.kill('SIGKILL');
                }
            }
            this.serverProcess = null;
        }
        this.isRunning = false;
        console.log('Python server stopped');
    }
    async sendShutdownSignal() {
        try {
            const response = await axios_1.default.post(`http://${this.config.host}:${this.config.port}/api/shutdown`, {}, {
                timeout: 2000
            });
            console.log('Shutdown signal sent successfully');
        }
        catch (error) {
            console.log('Could not send shutdown signal, will force kill');
        }
    }
    async findPythonExecutable() {
        // Try to find Python executable
        const pythonCommands = ['python3', 'python', 'py'];
        for (const cmd of pythonCommands) {
            try {
                const result = cp.execSync(`${cmd} --version`, { encoding: 'utf8' });
                if (result.includes('Python 3.')) {
                    return cmd;
                }
            }
            catch (error) {
                // Continue to next command
            }
        }
        // Check if Python extension is available
        const pythonExtension = vscode.extensions.getExtension('ms-python.python');
        if (pythonExtension) {
            try {
                const pythonPath = await vscode.commands.executeCommand('python.interpreterPath');
                if (pythonPath && typeof pythonPath === 'string') {
                    return pythonPath;
                }
            }
            catch (error) {
                console.warn('Could not get Python path from Python extension:', error);
            }
        }
        throw new Error('Python 3 executable not found. Please install Python 3 or ensure it is in your PATH.');
    }
    async isServerRunning() {
        try {
            const response = await axios_1.default.get(`http://${this.config.host}:${this.config.port}/health`, {
                timeout: 2000
            });
            return response.status === 200;
        }
        catch (error) {
            return false;
        }
    }
    async makeRequest(endpoint, method = 'GET', data) {
        if (!this.isRunning) {
            throw new Error('Python server is not running');
        }
        try {
            const url = `http://${this.config.host}:${this.config.port}/api${endpoint}`;
            const config = {
                method,
                url,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                },
                ...(data && { data })
            };
            const response = await (0, axios_1.default)(config);
            return {
                success: true,
                data: response.data
            };
        }
        catch (error) {
            console.error(`API request failed: ${error}`);
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: error.response?.data?.detail || error.message
                };
            }
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    getServerUrl() {
        return `http://${this.config.host}:${this.config.port}`;
    }
    isServerRunning() {
        return this.isRunning;
    }
}
exports.PythonServerManager = PythonServerManager;
//# sourceMappingURL=pythonServer.js.map